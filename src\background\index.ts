// Chrome 扩展后台脚本
// 处理扩展的后台逻辑和消息传递

import { RequestMonitor } from "./services/monitor"
import { MessageHandler } from "./handlers/message"
import { DownloadManager } from "./services/download"
import { HeaderManager } from "./services/header"
import { ConnectionManager } from "./handlers/connection"
import { SidepanelManager } from "./services/sidepanel"
import { CorsManager } from "./services/cors"
import type { Message } from "../types/network"

console.log("后台脚本已启动")

// 动态注册content script用于snapany.com的downloader页面
async function registerDownloaderContentScript() {
  try {
    // 先清理可能存在的旧脚本
    const existingScripts = await chrome.scripting.getRegisteredContentScripts()
    const downloaderScriptIds = existingScripts
      .filter(script => script.id?.startsWith('snapany-downloader'))
      .map(script => script.id!)

    if (downloaderScriptIds.length > 0) {
      await chrome.scripting.unregisterContentScripts({ ids: downloaderScriptIds })
      console.log('已清理旧的downloader content scripts')
    }

    // 注册新的content script，支持多种语言路径
    await chrome.scripting.registerContentScripts([
      {
        id: "snapany-downloader-main",
        matches: [
          "https://snapany.com/downloader*",           // 直接路径
          "https://snapany.com/zh/downloader*",        // 中文
          "https://snapany.com/en/downloader*",        // 英文
          "https://snapany.com/ja/downloader*",        // 日文
          "https://snapany.com/ko/downloader*",        // 韩文
          "https://snapany.com/fr/downloader*",        // 法文
          "https://snapany.com/de/downloader*",        // 德文
          "https://snapany.com/es/downloader*",        // 西班牙文
          "https://snapany.com/pt/downloader*",        // 葡萄牙文
          "https://snapany.com/ru/downloader*",        // 俄文
          "https://snapany.com/it/downloader*",        // 意大利文
          "https://snapany.com/ar/downloader*",        // 阿拉伯文
          "https://snapany.com/hi/downloader*",        // 印地文
          "https://snapany.com/th/downloader*",        // 泰文
          "https://snapany.com/vi/downloader*",        // 越南文
          "https://snapany.com/id/downloader*",        // 印尼文
          "https://snapany.com/ms/downloader*",        // 马来文
          "https://snapany.com/tr/downloader*",        // 土耳其文
          "https://snapany.com/pl/downloader*",        // 波兰文
          "https://snapany.com/nl/downloader*",        // 荷兰文
          "https://snapany.com/sv/downloader*",        // 瑞典文
          "https://snapany.com/da/downloader*",        // 丹麦文
          "https://snapany.com/no/downloader*",        // 挪威文
          "https://snapany.com/fi/downloader*",        // 芬兰文
        ],
        js: ["snapany-downloader.js"], // 使用专门的downloader content script
        runAt: "document_start",
        persistAcrossSessions: true
      }
    ])

    console.log('成功注册snapany downloader content script')
  } catch (error) {
    console.error('注册content script失败:', error)
  }
}

// 在扩展启动时注册content script
registerDownloaderContentScript()

// 创建核心模块实例
const requestMonitor = new RequestMonitor()
const headerManager = new HeaderManager()
const corsManager = new CorsManager()
const downloadManager = new DownloadManager(headerManager)
const connectionManager = new ConnectionManager(requestMonitor)
const sidepanelManager = new SidepanelManager()
const messageHandler = new MessageHandler(requestMonitor)

// 设置模块间的依赖关系
messageHandler.setDependencies({
  downloadManager,
  headerManager,
  sidepanelManager
})

// 设置请求监控更新回调
requestMonitor.setUpdateCallback(() => {
  connectionManager.notifyAllClients()
})

// 初始化扩展
async function initializeExtension() {
  try {
    // 初始化侧边栏行为
    await sidepanelManager.initializeSidePanelBehavior()

    // 启用CORS绕过功能
    await corsManager.enable()

    console.log("扩展初始化完成")
  } catch (error) {
    console.error("扩展初始化失败:", error)
  }
}

// 在扩展启动时初始化
initializeExtension()

// 监听来自内容脚本和popup的消息
chrome.runtime.onMessage.addListener((message: Message, sender, sendResponse) => {
  // 使用消息处理器处理消息
  messageHandler.handleMessage(message, sender, sendResponse)
  return true // 保持消息通道开放以支持异步响应
})

// 监听标签页关闭事件，清理下载任务
chrome.tabs.onRemoved.addListener((tabId: number) => {
  console.log(`标签页 ${tabId} 已关闭，清理相关下载任务`)
  downloadManager.cleanupTabTasks(tabId)
})

// 监听标签页更新事件，处理页面刷新
chrome.tabs.onUpdated.addListener((tabId: number, changeInfo, tab) => {
  // 当页面开始加载新内容时，清理旧的下载任务
  if (changeInfo.status === 'loading' && changeInfo.url) {
    console.log(`标签页 ${tabId} 开始加载新页面，清理旧的下载任务`)
    downloadManager.cleanupTabTasks(tabId)
  }
})


