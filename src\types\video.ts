export interface VideoData {
  title: string
  size: string
  url: string
  favIconUrl: string
  status: "completed" | "downloading"
  requestHeaders?: chrome.webRequest.HttpHeader[]
  domain?: string
  pageTitle?: string
  pageUrl?: string
  ext?: string
  requestId?: string
  isM3u8?: boolean
}

export interface VideoItemProps {
  video: VideoData
  showThumbnail?: boolean
  className?: string
  onPlayClick?: () => void
}

export interface VideoListProps {
  videos: VideoData[]
  className?: string
  tabId?: number
}