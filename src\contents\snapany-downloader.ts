// Snapany官网下载器页面的Content Script
// 专门处理 https://snapany.com/*/downloader 页面的通信

import {
  BackgroundMessageType,
  PageToExtensionMessageType,
  ExtensionToPageMessageType
} from "../types/network"
import type { Response } from "../types/network"
import type { PageMessage } from "../types"

console.log("Snapany Downloader Content Script 已加载")

// ==================== 工具函数 ====================

/**
 * 发送消息到background脚本
 */
async function sendMessageToBackground(type: BackgroundMessageType | PageToExtensionMessageType, payload?: any): Promise<Response> {
  return await chrome.runtime.sendMessage({
    type,
    payload
  })
}

/**
 * 通过background脚本发送消息到标签页
 */
async function sendMessageToTab(messageType: string, data: any, pageTaskId?: string): Promise<void> {
  await sendMessageToBackground(BackgroundMessageType.SEND_TO_TAB, {
    type: messageType,
    data,
    pageTaskId
  })
}

// ==================== 消息处理器 ====================

/**
 * 处理获取下载数据请求
 */
async function handleGetDownloadDataById(data: any): Promise<void> {
  try {
    console.log('处理获取下载数据请求，收到的data:', data);

    const requestId = data.data?.requestId || data.requestId;
    console.log('提取到的requestId:', requestId);

    if (!requestId) {
      throw new Error('缺少请求ID参数');
    }

    const response = await sendMessageToBackground(PageToExtensionMessageType.GET_DOWNLOAD_DATA_BY_ID, { requestId })

    window.postMessage({
      type: ExtensionToPageMessageType.DOWNLOAD_DATA_RESPONSE,
      data: response
    }, window.location.origin)
  } catch (error) {
    console.error('获取下载数据失败:', error)

    window.postMessage({
      type: ExtensionToPageMessageType.DOWNLOAD_DATA_RESPONSE,
      data: {
        success: false,
        error: error instanceof Error ? error.message : '获取下载数据失败'
      }
    }, window.location.origin)
  }
}

/**
 * 处理下载文件请求（仅用于设置请求头）
 */
async function handleDownloadFileWithHeaders(data: any): Promise<void> {
  try {
    console.log("处理设置请求头请求:", data)

    const actualData = data.data || data;
    console.log("提取到的实际数据:", actualData);

    const response = await sendMessageToBackground(PageToExtensionMessageType.DOWNLOAD_FILE_WITH_HEADERS, actualData)

    await sendMessageToTab(ExtensionToPageMessageType.DOWNLOAD_FILE_RESPONSE, response, actualData.pageTaskId)
  } catch (error) {
    console.error('设置请求头失败:', error)
    await sendMessageToTab(
      ExtensionToPageMessageType.DOWNLOAD_FILE_RESPONSE,
      {
        success: false,
        error: error instanceof Error ? error.message : '设置请求头失败'
      },
      data.pageTaskId || data.data?.pageTaskId
    )
  }
}

/**
 * 处理页面的ping消息，回应pong表示插件存在且正常工作
 */
async function handleExtensionPing(data: any): Promise<void> {
  console.log('收到页面ping消息，回应pong', data)

  window.postMessage({
    type: ExtensionToPageMessageType.EXTENSION_PONG,
    timestamp: Date.now(),
    pageTaskId: data.pageTaskId,
    pingTimestamp: data.timestamp
  }, window.location.origin)
}

// ==================== 消息处理映射 ====================

const PAGE_MESSAGE_HANDLERS = {
  [PageToExtensionMessageType.GET_DOWNLOAD_DATA_BY_ID]: handleGetDownloadDataById,
  [PageToExtensionMessageType.DOWNLOAD_FILE_WITH_HEADERS]: handleDownloadFileWithHeaders,
  [PageToExtensionMessageType.EXTENSION_PING]: handleExtensionPing,
} as const

// ==================== 事件监听和初始化 ====================

/**
 * 初始化下载器桥梁
 */
function initializeBridge(): void {
  console.log("初始化Snapany下载器桥梁")

  window.postMessage({
    type: ExtensionToPageMessageType.CONTENT_SCRIPT_READY,
    data: {}
  }, window.location.origin)
}

/**
 * 处理来自页面的消息
 */
async function handlePageMessage(event: MessageEvent<PageMessage>): Promise<void> {
  if (event.source !== window) return

  const { type, ...data } = event.data
  console.log("Snapany downloader content script收到消息:", type, data)

  // 忽略插件发给页面的响应消息
  if (Object.values(ExtensionToPageMessageType).includes(type as ExtensionToPageMessageType)) {
    return
  }

  try {
    const handler = PAGE_MESSAGE_HANDLERS[type as keyof typeof PAGE_MESSAGE_HANDLERS]
    if (handler) {
      await handler(data)
    } else {
      console.log("忽略未知消息类型:", type)
    }
  } catch (error) {
    console.error("处理消息时发生错误:", error)
  }
}

// 添加消息监听器
window.addEventListener("message", handlePageMessage, false)

// 初始化桥梁
initializeBridge()

console.log("Snapany Downloader Content Script 初始化完成")
